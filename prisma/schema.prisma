generator client {
  provider = "prisma-client-js"
}

// 数据库配置
datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// 用户模型
model User {
  id            String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email         String     @unique @db.VarChar(255)
  emailVerified DateTime?  @db.Timestamptz(6)
  username      String?    @unique @db.VarChar(100)
  phone         String?    @unique @db.VarChar(20)
  password      String?    @db.VarChar(255)
  name          String?    @db.VarChar(100)
  image         String?    @db.VarChar(500) // NextAuth需要的字段
  avatar        String?    @db.VarChar(500) // 头像URL
  avatarKey     String?    @db.VarChar(200) // R2对象键，用于删除
  bio           String?
  position      String?    @db.VarChar(100)
  company       String?    @db.VarChar(200)
  experience    Int?
  industry      String?    @db.VarChar(100)
  education     String?    @db.VarChar(200)
  skills        String[]   @default([])
  level         UserLevel? @default(NEWBIE)
  points        Int?       @default(0)
  reputation    Decimal?   @default(0.0) @db.Decimal(10, 2)

  // 文件存储相关
  storageUsed   BigInt?    @default(0)       // 已使用存储空间（字节）
  storageLimit  BigInt?    @default(*********) // 存储限制（100MB）

  // 用户资料完善度
  profileCompleteness Int? @default(0)       // 资料完整度百分比
  lastProfileUpdate   DateTime? @db.Timestamptz(6) // 最后更新资料时间

  // 社交统计
  followersCount Int?     @default(0)        // 关注者数量
  followingCount Int?     @default(0)        // 关注数量

  // 安全和验证
  emailVerificationToken String? @db.VarChar(255) // 邮箱验证令牌
  passwordResetToken     String? @db.VarChar(255) // 密码重置令牌
  passwordResetExpires   DateTime? @db.Timestamptz(6) // 密码重置过期时间
  twoFactorEnabled       Boolean? @default(false)  // 是否启用双因子认证
  twoFactorSecret        String?  @db.VarChar(255) // 双因子认证密钥
  isAnonymous   Boolean?   @default(false)
  isEmailPublic Boolean?   @default(false)
  isPhonePublic Boolean?   @default(false)
  isVerified    Boolean?   @default(false)
  isActive      Boolean?   @default(true)
  isBanned      Boolean?   @default(false)
  createdAt     DateTime?  @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime?  @default(now()) @updatedAt @db.Timestamptz(6)
  lastLogin     DateTime?  @db.Timestamptz(6)

  // 关系定义
  accounts        Account[]
  sessions        Session[]
  workExperiences WorkExperience[]

  // 添加发布内容的关系
  posts      Post[]
  comments   Comment[]
  ratings    Rating[]
  interviews Interview[]
  salaries   Salary[]
  likes      Like[]
  bookmarks  Bookmark[]
  reports    Report[]

  // 文件上传关系
  fileUploads FileUpload[]

  // 通知相关关系
  notifications        Notification[]        // 接收的通知
  sentNotifications    Notification[]        @relation("NotificationSender") // 发送的通知
  notificationSettings NotificationSetting? // 通知设置

  @@map("users")
}

// 账号模型
model Account {
  id                String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId            String  @db.Uuid
  type              String  @db.VarChar(50)
  provider          String  @db.VarChar(50)
  providerAccountId String  @db.VarChar(100)
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String? @db.VarChar(50)
  scope             String? @db.VarChar(200)
  id_token          String?
  session_state     String? @db.VarChar(200)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId], map: "unique_provider_account")
  @@map("accounts")
}

// 会话模型
model Session {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  sessionToken String   @unique @db.VarChar(255)
  userId       String   @db.Uuid
  expires      DateTime @db.Timestamptz(6)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// 验证令牌模型
model VerificationToken {
  identifier String   @db.VarChar(255)
  token      String   @unique @db.VarChar(255)
  expires    DateTime @db.Timestamptz(6)

  @@unique([identifier, token], map: "unique_identifier_token")
  @@map("verification_tokens")
}

// 公司模型
model Company {
  id            String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name          String       @unique @db.VarChar(200)
  nameEn        String?      @unique @db.VarChar(200)
  logo          String?      @db.VarChar(500)
  description   String?
  website       String?      @db.VarChar(500)
  industry      String?      @db.VarChar(100)
  size          CompanySize?
  foundedYear   Int?
  headquarters  String?      @db.VarChar(100)
  address       String?
  phone         String?      @db.VarChar(50)
  email         String?      @db.VarChar(255)
  isVerified    Boolean?     @default(false)
  isActive      Boolean?     @default(true)
  totalRatings  Int?         @default(0)
  averageRating Decimal?     @default(0.0) @db.Decimal(3, 2)
  totalSalaries Int?         @default(0)
  totalReviews  Int?         @default(0)
  createdAt     DateTime?    @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime?    @default(now()) @updatedAt @db.Timestamptz(6)

  // 关系定义
  posts      Post[]
  salaries   Salary[]
  interviews Interview[]
  ratings    Rating[]

  @@map("companies")
}

// 帖子模型
model Post {
  id           String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  title        String    @db.VarChar(200)
  content      String
  excerpt      String?   @db.VarChar(500)
  type         PostType? @default(DISCUSSION)
  category     String?   @db.VarChar(50)
  tags         String[]  @default([])
  companyId    String?   @db.Uuid
  authorId     String    @db.Uuid
  isAnonymous  Boolean?  @default(false)
  isPublished  Boolean?  @default(true)
  isPinned     Boolean?  @default(false)
  isLocked     Boolean?  @default(false)
  isDeleted    Boolean?  @default(false)
  viewCount    Int?      @default(0)
  likeCount    Int?      @default(0)
  commentCount Int?      @default(0)
  shareCount   Int?      @default(0)
  createdAt    DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt    DateTime? @default(now()) @updatedAt @db.Timestamptz(6)
  publishedAt  DateTime? @db.Timestamptz(6)

  // 关系定义
  author  User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  Company Company? @relation(fields: [companyId], references: [id])

  @@map("posts")
}

// 评论模型
model Comment {
  id          String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  content     String
  postId      String    @db.Uuid
  authorId    String    @db.Uuid
  parentId    String?   @db.Uuid
  isAnonymous Boolean?  @default(false)
  isDeleted   Boolean?  @default(false)
  likeCount   Int?      @default(0)
  replyCount  Int?      @default(0)
  createdAt   DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime? @default(now()) @updatedAt @db.Timestamptz(6)

  // 关系定义
  author User @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@map("comments")
}

// 点赞模型
model Like {
  id        String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId    String    @db.Uuid
  postId    String?   @db.Uuid
  commentId String?   @db.Uuid
  createdAt DateTime? @default(now()) @db.Timestamptz(6)

  // 关系定义
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, commentId], map: "unique_user_comment_like")
  @@unique([userId, postId], map: "unique_user_post_like")
  @@map("likes")
}

// 收藏模型
model Bookmark {
  id        String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId    String    @db.Uuid
  postId    String    @db.Uuid
  createdAt DateTime? @default(now()) @db.Timestamptz(6)
  User      User      @relation(fields: [userId], references: [id])

  @@unique([userId, postId], map: "unique_user_post_bookmark")
  @@map("bookmarks")
}

// 薪资模型
model Salary {
  id           String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  authorId     String    @db.Uuid
  companyId    String    @db.Uuid
  position     String    @db.VarChar(100)
  level        String?   @db.VarChar(50)
  department   String?   @db.VarChar(100)
  workLocation String?   @db.VarChar(100)
  workType     String?   @db.VarChar(50)
  experience   Int?
  education    String?   @db.VarChar(100)
  baseSalary   Decimal?  @db.Decimal(12, 2)
  bonus        Decimal?  @default(0) @db.Decimal(12, 2)
  stockOptions Decimal?  @default(0) @db.Decimal(12, 2)
  benefits     Decimal?  @default(0) @db.Decimal(12, 2)
  totalSalary  Decimal   @db.Decimal(12, 2)
  currency     String?   @default("CNY") @db.VarChar(10)
  salaryYear   Int
  notes        String?
  tags         String[]  @default([])
  isVerified   Boolean?  @default(false)
  isActive     Boolean?  @default(true)
  isAnonymous  Boolean?  @default(true)
  createdAt    DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt    DateTime? @default(now()) @updatedAt @db.Timestamptz(6)
  User         User      @relation(fields: [authorId], references: [id])
  Company      Company   @relation(fields: [companyId], references: [id])

  @@map("salaries")
}

// 面试模型
model Interview {
  id             String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  authorId       String              @db.Uuid
  companyId      String              @db.Uuid
  position       String              @db.VarChar(100)
  department     String?             @db.VarChar(100)
  interviewType  String?             @db.VarChar(50)
  interviewRound Int?                @default(1)
  interviewDate  DateTime?           @db.Date
  duration       Int?
  difficulty     InterviewDifficulty
  result         InterviewResult
  rating         Int?
  questions      String[]
  experience     String?
  tips           String?
  notes          String?
  isActive       Boolean?            @default(true)
  isAnonymous    Boolean?            @default(true)
  createdAt      DateTime?           @default(now()) @db.Timestamptz(6)
  updatedAt      DateTime?           @default(now()) @updatedAt @db.Timestamptz(6)
  User           User                @relation(fields: [authorId], references: [id])
  Company        Company             @relation(fields: [companyId], references: [id])

  @@map("interviews")
}

// 评分模型
model Rating {
  id                   String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  authorId             String    @db.Uuid
  companyId            String    @db.Uuid
  overallRating        Decimal   @db.Decimal(2, 1)
  workLifeBalance      Int?
  compensation         Int?
  culture              Int?
  careerGrowth         Int?
  management           Int?
  title                String?   @db.VarChar(200)
  pros                 String?
  cons                 String?
  advice               String?
  isRecommended        Boolean?
  recommendationReason String?
  position             String?   @db.VarChar(100)
  department           String?   @db.VarChar(100)
  workDuration         Int?
  employmentType       String?   @db.VarChar(50)
  isActive             Boolean?  @default(true)
  isAnonymous          Boolean?  @default(true)
  isVerified           Boolean?  @default(false)
  createdAt            DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt            DateTime? @default(now()) @updatedAt @db.Timestamptz(6)
  User                 User      @relation(fields: [authorId], references: [id])
  Company              Company   @relation(fields: [companyId], references: [id])

  @@unique([authorId, companyId], map: "unique_user_company_rating")
  @@map("ratings")
}

// 举报模型
model Report {
  id           String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  reporterId   String        @db.Uuid
  targetType   String        @db.VarChar(50)
  targetId     String        @db.Uuid
  reason       ReportReason
  description  String?
  status       ReportStatus? @default(PENDING)
  handlerId    String?       @db.Uuid
  handlerNotes String?
  resolvedAt   DateTime?     @db.Timestamptz(6)
  createdAt    DateTime?     @default(now()) @db.Timestamptz(6)
  updatedAt    DateTime?     @default(now()) @updatedAt @db.Timestamptz(6)
  User         User?         @relation(fields: [userId], references: [id])
  userId       String?       @db.Uuid

  @@map("reports")
}

// 工作经历模型
model WorkExperience {
  id                 String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId             String              @db.Uuid
  companyName        String              @db.VarChar(200)
  position           String              @db.VarChar(100)
  department         String?             @db.VarChar(100)
  employmentType     EmploymentType
  startDate          DateTime            @db.Date
  endDate            DateTime?           @db.Date
  isCurrent          Boolean?            @default(false)
  description        String?
  achievements       String[]
  skills             String[]
  salary             Decimal?            @db.Decimal(12, 2)
  currency           String?             @default("CNY") @db.VarChar(10)
  verificationStatus VerificationStatus? @default(PENDING)
  verifiedById       String?             @db.Uuid
  verifiedAt         DateTime?           @db.Timestamptz(6)
  isPublic           Boolean?            @default(false)
  createdAt          DateTime?           @default(now()) @db.Timestamptz(6)
  updatedAt          DateTime?           @default(now()) @updatedAt @db.Timestamptz(6)

  // 关系定义
  user  User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  files ExperienceFile[]

  @@map("work_experiences")
}

// 文件模型
model File {
  id           String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  originalName String    @db.VarChar(255)
  fileName     String    @db.VarChar(255)
  filePath     String    @db.VarChar(500)
  fileSize     Int
  mimeType     String    @db.VarChar(100)
  fileHash     String    @db.VarChar(64)
  type         String    @db.VarChar(50)
  description  String?
  relatedId    String?   @db.Uuid
  relatedType  String?   @db.VarChar(50)
  uploadedById String    @db.Uuid
  isDeleted    Boolean?  @default(false)
  createdAt    DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt    DateTime? @default(now()) @updatedAt @db.Timestamptz(6)

  @@unique([fileHash, uploadedById], map: "unique_file_hash_user")
  @@map("files")
}

// 经验文件模型
model ExperienceFile {
  id                 String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  workExperienceId   String              @db.Uuid
  fileName           String              @db.VarChar(255)
  fileUrl            String              @db.VarChar(500)
  fileSize           Int?
  mimeType           String?             @db.VarChar(100)
  category           FileCategory
  description        String?
  verificationStatus VerificationStatus? @default(PENDING)
  verifiedById       String?             @db.Uuid
  verifiedAt         DateTime?           @db.Timestamptz(6)
  uploadedAt         DateTime?           @default(now()) @db.Timestamptz(6)
  createdAt          DateTime?           @default(now()) @db.Timestamptz(6)
  updatedAt          DateTime?           @default(now()) @db.Timestamptz(6)

  // 关系定义
  workExperience WorkExperience @relation(fields: [workExperienceId], references: [id], onDelete: Cascade)

  @@map("experience_files")
}

// 验证记录模型
model VerificationRecord {
  id         String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  reviewerId String         @db.Uuid
  targetType TargetType
  targetId   String         @db.Uuid
  decision   ReviewDecision
  reason     String?
  notes      String?
  createdAt  DateTime?      @default(now()) @db.Timestamptz(6)

  @@map("verification_records")
}

// 用户信誉模型
model UserCredibility {
  id                      String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId                  String    @unique @db.Uuid
  credibilityScore        Decimal?  @default(0.0) @db.Decimal(5, 2)
  verifiedWorkExperiences Int?      @default(0)
  verifiedSalaries        Int?      @default(0)
  verifiedInterviews      Int?      @default(0)
  contributionPoints      Int?      @default(0)
  reportCount             Int?      @default(0)
  lastCalculatedAt        DateTime? @default(now()) @db.Timestamptz(6)
  createdAt               DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt               DateTime? @default(now()) @updatedAt @db.Timestamptz(6)

  @@map("user_credibility")
}

// 用户等级枚举
enum UserLevel {
  NEWBIE
  ACTIVE
  SENIOR
  EXPERT
  MODERATOR
  ADMIN
}

// 公司规模枚举
enum CompanySize {
  STARTUP
  SMALL
  MEDIUM
  LARGE
  ENTERPRISE
}

// 帖子类型枚举
enum PostType {
  DISCUSSION
  QUESTION
  SHARING
  NEWS
  REVIEW
  JOB
}

// 面试难度枚举
enum InterviewDifficulty {
  EASY
  MEDIUM
  HARD
  VERY_HARD
}

// 面试结果枚举
enum InterviewResult {
  PASSED
  FAILED
  PENDING
  CANCELLED
}

// 举报原因枚举
enum ReportReason {
  SPAM
  INAPPROPRIATE
  FAKE_INFO
  HARASSMENT
  COPYRIGHT
  OTHER
}

// 举报状态枚举
enum ReportStatus {
  PENDING
  REVIEWING
  RESOLVED
  REJECTED
}

// 雇佣类型枚举
enum EmploymentType {
  FULL_TIME
  PART_TIME
  CONTRACT
  INTERNSHIP
  FREELANCE
}

// 验证状态枚举
enum VerificationStatus {
  PENDING
  APPROVED
  REJECTED
  REVOKED
}

// 文件分类枚举
enum FileCategory {
  CONTRACT
  CERTIFICATE
  PHOTO
  DOCUMENT
  OTHER
}

// 目标类型枚举
enum TargetType {
  WORK_EXPERIENCE
  EXPERIENCE_FILE
  SALARY
  INTERVIEW
}

// 审核决策枚举
enum ReviewDecision {
  APPROVED
  REJECTED
  REVOKED
  PENDING_MORE_INFO
}

// 文件上传记录模型
model FileUpload {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId       String   @db.Uuid
  fileName     String   @db.VarChar(255)     // 存储的文件名
  originalName String   @db.VarChar(255)     // 原始文件名
  fileSize     BigInt                        // 文件大小（字节）
  contentType  String   @db.VarChar(100)     // MIME类型
  r2Key        String   @db.VarChar(500)     // R2对象键
  r2Url        String   @db.VarChar(500)     // 公共访问URL
  folder       FileFolder                    // 文件夹分类
  purpose      FilePurpose                   // 文件用途
  isActive     Boolean  @default(true)       // 是否有效
  isPublic     Boolean  @default(false)      // 是否公开访问
  downloadCount Int     @default(0)          // 下载次数
  metadata     Json?                         // 额外元数据
  createdAt    DateTime @default(now()) @db.Timestamptz(6)
  updatedAt    DateTime @updatedAt @db.Timestamptz(6)

  // 关系
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("file_uploads")
}

// 文件夹枚举
enum FileFolder {
  AVATARS       // 用户头像
  COMPANY_LOGOS // 企业Logo
  WORK_FILES    // 工作证明文件
  DOCUMENTS     // 文档
  ATTACHMENTS   // 附件
  TEMP          // 临时文件
}

// 文件用途枚举
enum FilePurpose {
  USER_AVATAR          // 用户头像
  COMPANY_LOGO         // 企业Logo
  WORK_CERTIFICATE     // 工作证明
  EDUCATION_CERTIFICATE // 教育证明
  RESUME               // 简历
  PORTFOLIO            // 作品集
  INTERVIEW_ATTACHMENT // 面试附件
  SALARY_PROOF         // 薪资证明
  OTHER                // 其他
}

// 通知类型枚举
enum NotificationType {
  // 社交通知
  LIKE              // 点赞
  COMMENT           // 评论
  REPLY             // 回复
  FOLLOW            // 关注
  MENTION           // 提及

  // 工作相关通知
  JOB_INVITATION    // 工作邀请
  INTERVIEW_INVITE  // 面试邀请
  SALARY_REQUEST    // 薪资询问
  COMPANY_UPDATE    // 企业更新

  // 内容通知
  POST_FEATURED     // 帖子被推荐
  POST_APPROVED     // 帖子审核通过
  POST_REJECTED     // 帖子被拒绝
  CONTENT_REPORTED  // 内容被举报

  // 系统通知
  SYSTEM_UPDATE     // 系统更新
  SECURITY_ALERT    // 安全提醒
  ACCOUNT_VERIFIED  // 账户验证
  POLICY_UPDATE     // 政策更新
  MAINTENANCE       // 系统维护

  // 私信通知
  PRIVATE_MESSAGE   // 私信
  GROUP_MESSAGE     // 群消息
}

// 通知优先级枚举
enum NotificationPriority {
  LOW       // 低优先级
  NORMAL    // 普通优先级
  HIGH      // 高优先级
  URGENT    // 紧急
}

// 通知状态枚举
enum NotificationStatus {
  UNREAD    // 未读
  READ      // 已读
  ARCHIVED  // 已归档
}

// 通知模型
model Notification {
  id          String               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId      String               @db.Uuid // 接收通知的用户
  type        NotificationType     // 通知类型
  title       String               @db.VarChar(200) // 通知标题
  content     String?              // 通知内容
  status      NotificationStatus   @default(UNREAD) // 通知状态
  priority    NotificationPriority @default(NORMAL) // 优先级

  // 关联数据
  relatedId   String?              @db.Uuid // 相关对象ID（如帖子ID、评论ID等）
  relatedType String?              @db.VarChar(50) // 相关对象类型（post、comment、user等）

  // 发送者信息
  senderId    String?              @db.Uuid // 发送者用户ID
  senderName  String?              @db.VarChar(100) // 发送者姓名（冗余字段，提高查询性能）
  senderAvatar String?             @db.VarChar(500) // 发送者头像（冗余字段）

  // 操作链接
  actionUrl   String?              @db.VarChar(500) // 点击通知后跳转的URL
  actionText  String?              @db.VarChar(50) // 操作按钮文本

  // 元数据
  metadata    Json?                // 额外的元数据（JSON格式）

  // 时间戳
  readAt      DateTime?            @db.Timestamptz(6) // 阅读时间
  archivedAt  DateTime?            @db.Timestamptz(6) // 归档时间
  expiresAt   DateTime?            @db.Timestamptz(6) // 过期时间
  createdAt   DateTime             @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime             @default(now()) @updatedAt @db.Timestamptz(6)

  // 关联关系
  user        User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  sender      User?                @relation("NotificationSender", fields: [senderId], references: [id], onDelete: SetNull)

  // 索引
  @@index([userId, status, createdAt])
  @@index([userId, type])
  @@index([relatedId, relatedType])
  @@index([createdAt])
  @@map("notifications")
}

// 通知设置模型
model NotificationSetting {
  id     String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId String @unique @db.Uuid // 用户ID

  // 社交通知设置
  enableLike           Boolean @default(true)  // 点赞通知
  enableComment        Boolean @default(true)  // 评论通知
  enableReply          Boolean @default(true)  // 回复通知
  enableFollow         Boolean @default(true)  // 关注通知
  enableMention        Boolean @default(true)  // 提及通知

  // 工作相关通知设置
  enableJobInvitation  Boolean @default(true)  // 工作邀请通知
  enableInterviewInvite Boolean @default(true) // 面试邀请通知
  enableSalaryRequest  Boolean @default(true)  // 薪资询问通知
  enableCompanyUpdate  Boolean @default(false) // 企业更新通知

  // 内容通知设置
  enablePostFeatured   Boolean @default(true)  // 帖子推荐通知
  enablePostApproved   Boolean @default(true)  // 帖子审核通过通知
  enablePostRejected   Boolean @default(true)  // 帖子被拒绝通知
  enableContentReported Boolean @default(true) // 内容举报通知

  // 系统通知设置
  enableSystemUpdate   Boolean @default(true)  // 系统更新通知
  enableSecurityAlert  Boolean @default(true)  // 安全提醒通知
  enableAccountVerified Boolean @default(true) // 账户验证通知
  enablePolicyUpdate   Boolean @default(false) // 政策更新通知
  enableMaintenance    Boolean @default(false) // 系统维护通知

  // 私信通知设置
  enablePrivateMessage Boolean @default(true)  // 私信通知
  enableGroupMessage   Boolean @default(true)  // 群消息通知

  // 通知渠道设置
  enableWebNotification   Boolean @default(true)  // 网页通知
  enableEmailNotification Boolean @default(false) // 邮件通知
  enablePushNotification Boolean @default(false) // 推送通知

  // 通知时间设置
  quietHoursStart String? @db.VarChar(5) // 免打扰开始时间 (HH:MM)
  quietHoursEnd   String? @db.VarChar(5) // 免打扰结束时间 (HH:MM)
  enableQuietHours Boolean @default(false) // 是否启用免打扰

  // 时间戳
  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @default(now()) @updatedAt @db.Timestamptz(6)

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notification_settings")
}
