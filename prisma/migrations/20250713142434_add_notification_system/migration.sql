-- CreateEnum
CREATE TYPE "NotificationType" AS ENUM ('LIKE', 'COMMENT', 'REPLY', 'FOLLOW', 'MENTION', 'JOB_INVITATION', 'INTERVIEW_INVITE', 'SALARY_REQUEST', 'COMPANY_UPDATE', 'POST_FEATURED', 'POST_APPROVED', 'POST_REJECTED', 'CONTENT_REPORTED', 'SYSTEM_UPDATE', 'SECURITY_ALERT', 'ACCOUNT_VERIFIED', 'POLICY_UPDATE', 'MAINTENANCE', 'PRIVATE_MESSAGE', 'GROUP_MESSAGE');

-- Create<PERSON>num
CREATE TYPE "NotificationPriority" AS ENUM ('LOW', 'NORMAL', 'HIGH', 'URGENT');

-- CreateEnum
CREATE TYPE "NotificationStatus" AS ENUM ('UNREAD', 'READ', 'ARCHIVED');

-- CreateTable
CREATE TABLE "notifications" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "type" "NotificationType" NOT NULL,
    "title" VARCHAR(200) NOT NULL,
    "content" TEXT,
    "status" "NotificationStatus" NOT NULL DEFAULT 'UNREAD',
    "priority" "NotificationPriority" NOT NULL DEFAULT 'NORMAL',
    "relatedId" UUID,
    "relatedType" VARCHAR(50),
    "senderId" UUID,
    "senderName" VARCHAR(100),
    "senderAvatar" VARCHAR(500),
    "actionUrl" VARCHAR(500),
    "actionText" VARCHAR(50),
    "metadata" JSONB,
    "readAt" TIMESTAMPTZ(6),
    "archivedAt" TIMESTAMPTZ(6),
    "expiresAt" TIMESTAMPTZ(6),
    "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "notification_settings" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "enableLike" BOOLEAN NOT NULL DEFAULT true,
    "enableComment" BOOLEAN NOT NULL DEFAULT true,
    "enableReply" BOOLEAN NOT NULL DEFAULT true,
    "enableFollow" BOOLEAN NOT NULL DEFAULT true,
    "enableMention" BOOLEAN NOT NULL DEFAULT true,
    "enableJobInvitation" BOOLEAN NOT NULL DEFAULT true,
    "enableInterviewInvite" BOOLEAN NOT NULL DEFAULT true,
    "enableSalaryRequest" BOOLEAN NOT NULL DEFAULT true,
    "enableCompanyUpdate" BOOLEAN NOT NULL DEFAULT false,
    "enablePostFeatured" BOOLEAN NOT NULL DEFAULT true,
    "enablePostApproved" BOOLEAN NOT NULL DEFAULT true,
    "enablePostRejected" BOOLEAN NOT NULL DEFAULT true,
    "enableContentReported" BOOLEAN NOT NULL DEFAULT true,
    "enableSystemUpdate" BOOLEAN NOT NULL DEFAULT true,
    "enableSecurityAlert" BOOLEAN NOT NULL DEFAULT true,
    "enableAccountVerified" BOOLEAN NOT NULL DEFAULT true,
    "enablePolicyUpdate" BOOLEAN NOT NULL DEFAULT false,
    "enableMaintenance" BOOLEAN NOT NULL DEFAULT false,
    "enablePrivateMessage" BOOLEAN NOT NULL DEFAULT true,
    "enableGroupMessage" BOOLEAN NOT NULL DEFAULT true,
    "enableWebNotification" BOOLEAN NOT NULL DEFAULT true,
    "enableEmailNotification" BOOLEAN NOT NULL DEFAULT false,
    "enablePushNotification" BOOLEAN NOT NULL DEFAULT false,
    "quietHoursStart" VARCHAR(5),
    "quietHoursEnd" VARCHAR(5),
    "enableQuietHours" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "notification_settings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "notifications_userId_status_createdAt_idx" ON "notifications"("userId", "status", "createdAt");

-- CreateIndex
CREATE INDEX "notifications_userId_type_idx" ON "notifications"("userId", "type");

-- CreateIndex
CREATE INDEX "notifications_relatedId_relatedType_idx" ON "notifications"("relatedId", "relatedType");

-- CreateIndex
CREATE INDEX "notifications_createdAt_idx" ON "notifications"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "notification_settings_userId_key" ON "notification_settings"("userId");

-- AddForeignKey
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_senderId_fkey" FOREIGN KEY ("senderId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notification_settings" ADD CONSTRAINT "notification_settings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
